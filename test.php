<?php

require __DIR__.'/vendor/autoload.php';

try {
    $app = require_once __DIR__.'/bootstrap/app.php';
    
    // Test database connection
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=invoices', 'root', '');
    echo "Database connection successful\n";
    
    // Test if we can query the database
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "Found " . count($tables) . " tables in database\n";
    
    echo "Application bootstrap successful\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
