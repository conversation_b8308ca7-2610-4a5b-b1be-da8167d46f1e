<?php

namespace App\Http\Responses;

use App\Models\Role;
use App\Models\User;
use Filament\Notifications\Notification;
use Filament\Http\Responses\Auth\Contracts\LoginResponse as LoginResponseContract;

class LoginResponse implements LoginResponseContract
{
    public function toResponse($request)
    {
        /** @var User $user */
        $user = auth()->user();

        if ($user) {
            $role = $user->roles()->first();
            if ($role && $role->name === Role::ROLE_ADMIN) {
                return redirect()->intended('/admin');
            }

            if ($role && $role->name === Role::ROLE_CLIENT) {
                return redirect()->intended('/client');
            }
        }

        // Default fallback
        return redirect()->intended('/admin');
    }
}
